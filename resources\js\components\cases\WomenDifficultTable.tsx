import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface WomenDifficultCase {
    id: number;
    caseloadNo: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthdate: string;
    barangay: string;
    address: string;
    municipality: string;
    province: string;
    educational: string;
    attainment: string;
    previousOffense: string;
    typeOfOffense: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string;
    referral: string;
    other: string;
    status: string;
}

interface WomenDifficultTableProps {
    cases: WomenDifficultCase[];
    onEdit?: (caseItem: WomenDifficultCase) => void;
    onDelete?: (caseItem: WomenDifficultCase) => void;
    onView?: (caseItem: WomenDifficultCase) => void;
}

export default function WomenDifficultTable({ cases, onEdit, onDelete, onView }: WomenDifficultTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<WomenDifficultCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: WomenDifficultCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: WomenDifficultCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name of Client',
            selector: (row: WomenDifficultCase) => row.nameOfClient,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Age',
            selector: (row: WomenDifficultCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Sex',
            selector: (row: WomenDifficultCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Birthdate',
            selector: (row: WomenDifficultCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Barangay',
            selector: (row: WomenDifficultCase) => row.barangay,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Address',
            selector: (row: WomenDifficultCase) => row.address,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Municipality',
            selector: (row: WomenDifficultCase) => row.municipality,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Province',
            selector: (row: WomenDifficultCase) => row.province,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Educational',
            selector: (row: WomenDifficultCase) => row.educational,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Attainment',
            selector: (row: WomenDifficultCase) => row.attainment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Previous Offense',
            selector: (row: WomenDifficultCase) => row.previousOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Type of Offense',
            selector: (row: WomenDifficultCase) => row.typeOfOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Source of Referral',
            selector: (row: WomenDifficultCase) => row.sourceOfReferral,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Method',
            selector: (row: WomenDifficultCase) => row.method,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Initial',
            selector: (row: WomenDifficultCase) => row.initial,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Assessment',
            selector: (row: WomenDifficultCase) => row.assessment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Intervention',
            selector: (row: WomenDifficultCase) => row.intervention,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: WomenDifficultCase) => row.assistance,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Final',
            selector: (row: WomenDifficultCase) => row.final,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Evaluation',
            selector: (row: WomenDifficultCase) => row.evaluation,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Termination',
            selector: (row: WomenDifficultCase) => row.termination,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance 2',
            selector: (row: WomenDifficultCase) => row.assistance2,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Referral',
            selector: (row: WomenDifficultCase) => row.referral,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Other',
            selector: (row: WomenDifficultCase) => row.other,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Status',
            selector: (row: WomenDifficultCase) => row.status,
            sortable: true,
            width: '100px',
        },
    ];

    return (
        <BaseDataTable
            title="Inventory of Cases of Women in Especially Difficult Circumstances"
            subtitle="Municipality of Balagtas"
            data={cases}
            columns={columns}
            searchPlaceholder="Search women in difficult circumstances cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
