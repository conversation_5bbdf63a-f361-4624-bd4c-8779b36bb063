import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface ChildrenProtectionCase {
    id: number;
    caseloadNumber: string;
    date: string;
    nameOfChild: string;
    age: number;
    sex: string;
    birthdate: string;
    barangay: string;
    municipality: string;
    province: string;
    educational: string;
    attainment: string;
    slw: string;
    caregiver: string;
    caseCategory: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string;
    referral: string;
    other: string;
    status: string;
}

interface ChildrenProtectionTableProps {
    cases: ChildrenProtectionCase[];
    onEdit?: (caseItem: ChildrenProtectionCase) => void;
    onDelete?: (caseItem: ChildrenProtectionCase) => void;
    onView?: (caseItem: ChildrenProtectionCase) => void;
}

export default function ChildrenProtectionTable({ cases, onEdit, onDelete, onView }: ChildrenProtectionTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<ChildrenProtectionCase>[] = [
        {
            name: 'Caseload Number',
            selector: (row: ChildrenProtectionCase) => row.caseloadNumber,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: ChildrenProtectionCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name of Child',
            selector: (row: ChildrenProtectionCase) => row.nameOfChild,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Age',
            selector: (row: ChildrenProtectionCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Sex',
            selector: (row: ChildrenProtectionCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Birthdate',
            selector: (row: ChildrenProtectionCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Barangay',
            selector: (row: ChildrenProtectionCase) => row.barangay,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Municipality',
            selector: (row: ChildrenProtectionCase) => row.municipality,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Province',
            selector: (row: ChildrenProtectionCase) => row.province,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Educational',
            selector: (row: ChildrenProtectionCase) => row.educational,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Attainment',
            selector: (row: ChildrenProtectionCase) => row.attainment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'SLW',
            selector: (row: ChildrenProtectionCase) => row.slw,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Caregiver',
            selector: (row: ChildrenProtectionCase) => row.caregiver,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Case Category',
            selector: (row: ChildrenProtectionCase) => row.caseCategory,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Source of Referral',
            selector: (row: ChildrenProtectionCase) => row.sourceOfReferral,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Method',
            selector: (row: ChildrenProtectionCase) => row.method,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Initial',
            selector: (row: ChildrenProtectionCase) => row.initial,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Assessment',
            selector: (row: ChildrenProtectionCase) => row.assessment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Intervention',
            selector: (row: ChildrenProtectionCase) => row.intervention,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: ChildrenProtectionCase) => row.assistance,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Final',
            selector: (row: ChildrenProtectionCase) => row.final,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Evaluation',
            selector: (row: ChildrenProtectionCase) => row.evaluation,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Termination',
            selector: (row: ChildrenProtectionCase) => row.termination,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: ChildrenProtectionCase) => row.assistance2,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Referral',
            selector: (row: ChildrenProtectionCase) => row.referral,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Other',
            selector: (row: ChildrenProtectionCase) => row.other,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Status',
            selector: (row: ChildrenProtectionCase) => row.status,
            sortable: true,
            width: '100px',
        },
    ];

    return (
        <BaseDataTable
            title="CHILDREN IN NEED SPECIAL PROTECTION"
            subtitle="MUNICIPALITY OF BALAGTAS"
            data={cases}
            columns={columns}
            searchPlaceholder="Search children protection cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
