import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface OSAECCase {
    id: number;
    caseloadNo: string;
    date: string;
    names: string;
    birthdate: string;
    age: number;
    address: string;
    contactNumber: string;
    typeOfViolation: string;
    dateReported: string;
    noOfMonthsForCounselling: number;
    dateFinishedCounselling: string;
    remarks: string;
}

interface OSAECTableProps {
    cases: OSAECCase[];
    onEdit?: (caseItem: OSAECCase) => void;
    onDelete?: (caseItem: OSAECCase) => void;
    onView?: (caseItem: OSAECCase) => void;
}

export default function OSAECTable({ cases, onEdit, onDelete, onView }: OSAECTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<OSAECCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: OSAECCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: OSAECCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Names',
            selector: (row: OSAECCase) => row.names,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Birthdate',
            selector: (row: OSAECCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Age',
            selector: (row: OSAECCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Address',
            selector: (row: OSAECCase) => row.address,
            sortable: true,
            width: '200px',
        },
        {
            name: 'Contact Number',
            selector: (row: OSAECCase) => row.contactNumber,
            sortable: true,
            width: '130px',
        },
        {
            name: 'Type of Violation',
            selector: (row: OSAECCase) => row.typeOfViolation,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Date Reported',
            selector: (row: OSAECCase) => row.dateReported,
            sortable: true,
            width: '120px',
        },
        {
            name: 'No. of Months for Counselling',
            selector: (row: OSAECCase) => row.noOfMonthsForCounselling,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Date Finished Counselling',
            selector: (row: OSAECCase) => row.dateFinishedCounselling,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Remarks',
            selector: (row: OSAECCase) => row.remarks,
            sortable: true,
            width: '200px',
        },
    ];

    return (
        <BaseDataTable
            title="INVENTORY OF ONLINE SEXUAL ABUSE"
            subtitle="MUNICIPALITY OF BALAGTAS"
            data={cases}
            columns={columns}
            searchPlaceholder="Search OSAEC cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
