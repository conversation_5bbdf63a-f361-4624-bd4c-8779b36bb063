import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface ChildrenConflictCase {
    id: number;
    caseloadNo: string;
    date: string;
    nameOfChild: string;
    age: number;
    sex: string;
    birthdate: string;
    barangay: string;
    address: string;
    municipality: string;
    province: string;
    educational: string;
    attainment: string;
    previousOffense: string;
    typeOfOffense: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string;
    referral: string;
    other: string;
    status: string;
}

interface ChildrenConflictTableProps {
    cases: ChildrenConflictCase[];
    onEdit?: (caseItem: ChildrenConflictCase) => void;
    onDelete?: (caseItem: ChildrenConflictCase) => void;
    onView?: (caseItem: ChildrenConflictCase) => void;
}

export default function ChildrenConflictTable({ cases, onEdit, onDelete, onView }: ChildrenConflictTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<ChildrenConflictCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: ChildrenConflictCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: ChildrenConflictCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name of Child',
            selector: (row: ChildrenConflictCase) => row.nameOfChild,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Age',
            selector: (row: ChildrenConflictCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Sex',
            selector: (row: ChildrenConflictCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Birthdate',
            selector: (row: ChildrenConflictCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Barangay',
            selector: (row: ChildrenConflictCase) => row.barangay,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Address',
            selector: (row: ChildrenConflictCase) => row.address,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Municipality',
            selector: (row: ChildrenConflictCase) => row.municipality,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Province',
            selector: (row: ChildrenConflictCase) => row.province,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Educational',
            selector: (row: ChildrenConflictCase) => row.educational,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Attainment',
            selector: (row: ChildrenConflictCase) => row.attainment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Previous Offense',
            selector: (row: ChildrenConflictCase) => row.previousOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Type of Offense',
            selector: (row: ChildrenConflictCase) => row.typeOfOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Source of Referral',
            selector: (row: ChildrenConflictCase) => row.sourceOfReferral,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Method',
            selector: (row: ChildrenConflictCase) => row.method,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Initial',
            selector: (row: ChildrenConflictCase) => row.initial,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Assessment',
            selector: (row: ChildrenConflictCase) => row.assessment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Intervention',
            selector: (row: ChildrenConflictCase) => row.intervention,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: ChildrenConflictCase) => row.assistance,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Final',
            selector: (row: ChildrenConflictCase) => row.final,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Evaluation',
            selector: (row: ChildrenConflictCase) => row.evaluation,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Termination',
            selector: (row: ChildrenConflictCase) => row.termination,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance 2',
            selector: (row: ChildrenConflictCase) => row.assistance2,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Referral',
            selector: (row: ChildrenConflictCase) => row.referral,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Other',
            selector: (row: ChildrenConflictCase) => row.other,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Status',
            selector: (row: ChildrenConflictCase) => row.status,
            sortable: true,
            width: '100px',
        },
    ];

    return (
        <BaseDataTable
            title="CHILDREN IN CONFLICT WITH THE LAW"
            subtitle="BALAGTAS KANLUNGAN DESK IN CENTER"
            data={cases}
            columns={columns}
            searchPlaceholder="Search children in conflict cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
