import { useState } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ArrowUpDown, MoreHorizontal, Edit, Trash2, Eye } from 'lucide-react';

interface BahayKanlunganCase {
    id: number;
    caseloadNumber: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthday: string;
    address: string;
    barangay: string;
    municipality: string;
    province: string;
    educationalAttainment: string;
    naField: string; // N/A/N/A field from the image
    caseCategory: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string; // Second assistance column
    referral: string;
    other: string;
    status: string;
}

interface BahayKanlunganTableProps {
    cases: BahayKanlunganCase[];
    onEdit?: (caseItem: BahayKanlunganCase) => void;
    onDelete?: (caseItem: BahayKanlunganCase) => void;
    onView?: (caseItem: BahayKanlunganCase) => void;
}

export default function BahayKanlunganTable({ cases, onEdit, onDelete, onView }: BahayKanlunganTableProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const filteredCases = cases.filter(caseItem =>
        Object.values(caseItem).some(value =>
            value?.toString().toLowerCase().includes(searchQuery.toLowerCase())
        )
    );

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;
        
        const aValue = a[sortField as keyof BahayKanlunganCase];
        const bValue = b[sortField as keyof BahayKanlunganCase];
        
        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    return (
        <div className="space-y-4">
            {/* Search */}
            <div className="flex items-center gap-4">
                <Input
                    placeholder="Search Bahay Kanlungan cases..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="max-w-sm"
                />
            </div>

            {/* Table */}
            <div className="border border-gray-800 rounded-lg overflow-hidden">
                <div className="bg-red-600 text-white text-center py-2 font-bold text-sm">
                    MASTERLIST ADMITTED IN BAHAY KANLUNGAN<br />
                    BALAGTAS KANLUNGAN DROP-IN CENTER <br />
                    MUNICIPALITY OF BALAGTAS<br />
                </div>
                
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gray-100 border-b border-gray-800">
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">
                                <Button variant="ghost" onClick={() => handleSort('caseloadNumber')} className="h-auto p-0 font-bold text-xs">
                                    Caseload Number
                                    <ArrowUpDown className="ml-1 h-3 w-3" />
                                </Button>
                            </TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Date</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Name of Client (Fullname)</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Age</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Sex</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Birthday</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Address</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Barangay</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Municipality</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Province</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Educational Attainment</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">N/A/N/A</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Case Category</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Source of Referral</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Method</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Initial</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Assessment</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Intervention</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Assistance</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Final</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Evaluation</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Termination</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Assistance</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Referral</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Other</TableHead>
                            <TableHead className="border-r border-gray-800 text-center font-bold text-xs p-2">Status</TableHead>
                            <TableHead className="text-center font-bold text-xs p-2">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {sortedCases.map((caseItem) => (
                            <TableRow key={caseItem.id} className="hover:bg-gray-50 border-b border-gray-800">
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.caseloadNumber}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.date}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.nameOfClient}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.age}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.sex}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.birthday}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.address}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.barangay}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.municipality}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.province}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.educationalAttainment}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.naField}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.caseCategory}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.sourceOfReferral}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.method}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.initial}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.assessment}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.intervention}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.assistance}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.final}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.evaluation}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.termination}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.assistance2}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.referral}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.other}</TableCell>
                                <TableCell className="border-r border-gray-800 text-center text-xs p-2">{caseItem.status}</TableCell>
                                <TableCell className="text-center p-2">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onView?.(caseItem)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                View
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onEdit?.(caseItem)}>
                                                <Edit className="mr-2 h-4 w-4" />
                                                Edit
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => onDelete?.(caseItem)} className="text-red-600">
                                                <Trash2 className="mr-2 h-4 w-4" />
                                                Delete
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
}
