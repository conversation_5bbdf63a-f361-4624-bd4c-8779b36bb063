import React from 'react';

interface CaseloadRecord {
    id: number;
    caseloadNumber: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthday: string;
    barangay: string;
    municipality: string;
    province: string;
    educationalAttainment: {
        new: string;
        carryOver: string;
    };
    caseCategory: string;
    sourceOfReferral: string;
    method: string;
    servicesProvided: {
        initial: string;
        transportation: string;
        educational: string;
        psychological: string;
        safety: string;
        counseling: string;
        livelihood: string;
        referral: string;
        other: string;
    };
    status: string;
}

interface ComplexCaseloadTableProps {
    data: CaseloadRecord[];
    title?: string;
    subtitle?: string;
}

export default function ComplexCaseloadTable({ 
    data, 
    title = "SOCIAL WORK CASELOAD RECORDS",
    subtitle = "MUNICIPALITY OF BALAGTAS"
}: ComplexCaseloadTableProps) {
    return (
        <div className="w-full overflow-x-auto">
            {/* Header */}
            <div className="bg-red-600 text-white text-center py-2 font-bold text-sm mb-4 rounded-t-lg">
                {title}
                {subtitle && <><br />{subtitle}</>}
            </div>

            {/* Complex Table with Merged Headers */}
            <table className="w-full border-collapse border border-gray-800 text-xs">
                <thead>
                    {/* First Header Row - Main Categories */}
                    <tr className="bg-gray-100">
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Caseload Number
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Date
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[150px]">
                            Name of Client (Fullname)
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[50px]">
                            Age
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[50px]">
                            Sex
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Birthday
                        </th>
                        <th colSpan={3} className="border border-gray-800 p-2 text-center font-bold bg-blue-50">
                            Address
                        </th>
                        <th colSpan={2} className="border border-gray-800 p-2 text-center font-bold bg-green-50">
                            Educational Attainment
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[100px]">
                            Case Category (Type of Abuse)
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[100px]">
                            Source Of Referral
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Method
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Referral
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Other
                        </th>
                        <th colSpan={9} className="border border-gray-800 p-2 text-center font-bold bg-yellow-50">
                            Services Provided (Please Check) (Please Indicate Amount for assistance)
                        </th>
                        <th rowSpan={2} className="border border-gray-800 p-2 text-center font-bold min-w-[80px]">
                            Status
                        </th>
                    </tr>
                    
                    {/* Second Header Row - Sub Categories */}
                    <tr className="bg-gray-100">
                        {/* Address Sub-headers */}
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-blue-50">
                            Barangay
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-blue-50">
                            Municipality
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-blue-50">
                            Province
                        </th>
                        
                        {/* Educational Attainment Sub-headers */}
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-green-50">
                            NEW
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-green-50">
                            Carry-over
                        </th>
                        
                        {/* Services Provided Sub-headers */}
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Initial Assistance
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Transportation Assistance
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Educational Assistance
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Psychological Evaluation
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Safety (for Victim)
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Counseling
                        </th>
                        <th className="border border-gray-800 p-1 text-center font-bold text-xs bg-yellow-50">
                            Livelihood Assistance
                        </th>
                    </tr>
                </thead>
                
                <tbody>
                    {data.length === 0 ? (
                        <tr>
                            <td colSpan={21} className="border border-gray-800 p-4 text-center text-gray-500">
                                No records found
                            </td>
                        </tr>
                    ) : (
                        data.map((record) => (
                            <tr key={record.id} className="hover:bg-gray-50">
                                <td className="border border-gray-800 p-2 text-center">{record.caseloadNumber}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.date}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.nameOfClient}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.age}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.sex}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.birthday}</td>
                                
                                {/* Address columns */}
                                <td className="border border-gray-800 p-2 text-center">{record.barangay}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.municipality}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.province}</td>
                                
                                {/* Educational Attainment columns */}
                                <td className="border border-gray-800 p-2 text-center">{record.educationalAttainment.new}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.educationalAttainment.carryOver}</td>
                                
                                <td className="border border-gray-800 p-2 text-center">{record.caseCategory}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.sourceOfReferral}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.method}</td>
                                
                                {/* Services Provided columns */}
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.initial}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.transportation}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.educational}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.psychological}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.safety}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.counseling}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.livelihood}</td>
                                
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.referral}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.servicesProvided.other}</td>
                                <td className="border border-gray-800 p-2 text-center">{record.status}</td>
                            </tr>
                        ))
                    )}
                </tbody>
            </table>
            
            {/* Footer Note */}
            <div className="mt-4 text-xs text-gray-600 italic">
                * This table reproduces the exact Excel format with merged headers and grouped columns
            </div>
        </div>
    );
}

// Sample data for demonstration
export const sampleCaseloadData: CaseloadRecord[] = [
    {
        id: 1,
        caseloadNumber: "2024-001",
        date: "2024-01-15",
        nameOfClient: "Maria Santos Cruz",
        age: 28,
        sex: "Female",
        birthday: "1996-03-12",
        barangay: "Santol",
        municipality: "Balagtas",
        province: "Bulacan",
        educationalAttainment: {
            new: "High School Graduate",
            carryOver: "N/A"
        },
        caseCategory: "Domestic Violence",
        sourceOfReferral: "Barangay Officials",
        method: "Walk-in",
        servicesProvided: {
            initial: "₱5,000",
            transportation: "₱1,000",
            educational: "N/A",
            psychological: "Completed",
            safety: "Temporary Shelter",
            counseling: "Ongoing",
            livelihood: "Skills Training",
            referral: "DSWD",
            other: "Legal Assistance"
        },
        status: "Active"
    },
    {
        id: 2,
        caseloadNumber: "2024-002",
        date: "2024-01-20",
        nameOfClient: "Juan Miguel Reyes",
        age: 15,
        sex: "Male",
        birthday: "2009-07-08",
        barangay: "Borol 1st",
        municipality: "Balagtas",
        province: "Bulacan",
        educationalAttainment: {
            new: "Grade 9",
            carryOver: "Grade 8"
        },
        caseCategory: "Child in Conflict with Law",
        sourceOfReferral: "Police",
        method: "Referral",
        servicesProvided: {
            initial: "₱3,000",
            transportation: "₱500",
            educational: "₱8,000",
            psychological: "Scheduled",
            safety: "N/A",
            counseling: "Weekly",
            livelihood: "Skills Training",
            referral: "TESDA",
            other: "Family Counseling"
        },
        status: "Active"
    }
];
