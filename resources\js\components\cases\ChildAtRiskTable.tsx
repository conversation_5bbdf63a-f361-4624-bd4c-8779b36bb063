import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface ChildAtRiskCase {
    id: number;
    caseloadNo: string;
    dateOfReferral: string;
    nameOfCAR: string;
    address: string;
    brgy: string;
    address2: string;
    birthdate: string;
    sex: string;
    age: number;
    nameOfGuardian: string;
    addressOfGuardian: string;
    highestEducational: string;
    offenseCommitted: string;
    remarks: string;
}

interface ChildAtRiskTableProps {
    cases: ChildAtRiskCase[];
    onEdit?: (caseItem: ChildAtRiskCase) => void;
    onDelete?: (caseItem: ChildAtRiskCase) => void;
    onView?: (caseItem: ChildAtRiskCase) => void;
}

export default function ChildAtRiskTable({ cases, onEdit, onDelete, onView }: ChildAtRiskTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<ChildAtRiskCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: ChildAtRiskCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date of Referral',
            selector: (row: ChildAtRiskCase) => row.dateOfReferral,
            sortable: true,
            width: '130px',
        },
        {
            name: 'Name of CAR',
            selector: (row: ChildAtRiskCase) => row.nameOfCAR,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Address',
            selector: (row: ChildAtRiskCase) => row.address,
            sortable: true,
            width: '200px',
        },
        {
            name: 'Barangay',
            selector: (row: ChildAtRiskCase) => row.brgy,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Address 2',
            selector: (row: ChildAtRiskCase) => row.address2,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Birthdate',
            selector: (row: ChildAtRiskCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Sex',
            selector: (row: ChildAtRiskCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Age',
            selector: (row: ChildAtRiskCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Name of Guardian',
            selector: (row: ChildAtRiskCase) => row.nameOfGuardian,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Address of Guardian',
            selector: (row: ChildAtRiskCase) => row.addressOfGuardian,
            sortable: true,
            width: '200px',
        },
        {
            name: 'Highest Educational',
            selector: (row: ChildAtRiskCase) => row.highestEducational,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Offense Committed',
            selector: (row: ChildAtRiskCase) => row.offenseCommitted,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Remarks',
            selector: (row: ChildAtRiskCase) => row.remarks,
            sortable: true,
            width: '200px',
        },
    ];

    return (
        <BaseDataTable
            title="MASTERLIST OF CAR"
            subtitle="MUNICIPALITY OF BALAGTAS"
            data={cases}
            columns={columns}
            searchPlaceholder="Search child at risk cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
