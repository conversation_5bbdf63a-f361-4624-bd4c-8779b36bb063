import React from 'react';
import DataTable, { TableColumn } from 'react-data-table-component';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, Eye, Search } from 'lucide-react';

// Custom styles for Excel-like appearance with purple government theme
const customStyles = {
    table: {
        style: {
            border: '1px solid #374151',
            borderRadius: '8px',
            overflow: 'hidden',
        },
    },
    header: {
        style: {
            backgroundColor: '#7c3aed',
            color: 'white',
            fontSize: '14px',
            fontWeight: 'bold',
            padding: '12px',
            borderBottom: '2px solid #374151',
        },
    },
    headRow: {
        style: {
            backgroundColor: '#f3f4f6',
            borderBottom: '1px solid #374151',
            minHeight: '40px',
        },
    },
    headCells: {
        style: {
            fontSize: '12px',
            fontWeight: 'bold',
            color: '#374151',
            padding: '8px 4px',
            borderRight: '1px solid #374151',
            textAlign: 'center',
            '&:last-child': {
                borderRight: 'none',
            },
        },
    },
    cells: {
        style: {
            fontSize: '11px',
            padding: '6px 4px',
            borderRight: '1px solid #e5e7eb',
            textAlign: 'center',
            '&:last-child': {
                borderRight: 'none',
            },
        },
    },
    rows: {
        style: {
            borderBottom: '1px solid #e5e7eb',
            '&:hover': {
                backgroundColor: '#f9fafb',
            },
        },
        stripedStyle: {
            backgroundColor: '#f8fafc',
        },
    },
    pagination: {
        style: {
            borderTop: '1px solid #374151',
            backgroundColor: '#f3f4f6',
        },
        pageButtonsStyle: {
            borderRadius: '4px',
            height: '32px',
            padding: '4px 8px',
            margin: '0 2px',
            cursor: 'pointer',
            transition: 'all 0.2s',
            color: '#7c3aed',
            fill: '#7c3aed',
            backgroundColor: 'transparent',
            '&:disabled': {
                cursor: 'unset',
                color: '#9ca3af',
                fill: '#9ca3af',
            },
            '&:hover:not(:disabled)': {
                backgroundColor: '#7c3aed',
                color: 'white',
            },
        },
    },
};

interface BaseDataTableProps<T> {
    title: string;
    subtitle?: string;
    data: T[];
    columns: TableColumn<T>[];
    searchPlaceholder?: string;
    onEdit?: (row: T) => void;
    onDelete?: (row: T) => void;
    onView?: (row: T) => void;
    searchable?: boolean;
    pagination?: boolean;
    paginationPerPage?: number;
    striped?: boolean;
    highlightOnHover?: boolean;
    responsive?: boolean;
}

// Action column component
export function createActionColumn<T>(
    onEdit?: (row: T) => void,
    onDelete?: (row: T) => void,
    onView?: (row: T) => void
): TableColumn<T> {
    return {
        name: 'Actions',
        cell: (row: T) => (
            <div className="flex justify-center">
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                        {onView && (
                            <DropdownMenuItem onClick={() => onView(row)} className="cursor-pointer">
                                <Eye className="mr-2 h-4 w-4" />
                                View
                            </DropdownMenuItem>
                        )}
                        {onEdit && (
                            <DropdownMenuItem onClick={() => onEdit(row)} className="cursor-pointer">
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                        )}
                        {onDelete && (
                            <DropdownMenuItem onClick={() => onDelete(row)} className="text-red-600 cursor-pointer">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        ),
        ignoreRowClick: true,
        allowOverflow: true,
        button: true,
        width: '100px',
        center: true,
    };
}

export default function BaseDataTable<T>({
    title,
    subtitle,
    data,
    columns,
    searchPlaceholder = "Search...",
    onEdit,
    onDelete,
    onView,
    searchable = true,
    pagination = true,
    paginationPerPage = 10,
    striped = true,
    highlightOnHover = true,
    responsive = true,
}: BaseDataTableProps<T>) {
    const [filterText, setFilterText] = React.useState('');
    const [resetPaginationToggle, setResetPaginationToggle] = React.useState(false);

    // Add action column if any action handlers are provided
    const finalColumns = React.useMemo(() => {
        const cols = [...columns];
        if (onEdit || onDelete || onView) {
            cols.push(createActionColumn(onEdit, onDelete, onView));
        }
        return cols;
    }, [columns, onEdit, onDelete, onView]);

    // Filter data based on search text
    const filteredItems = React.useMemo(() => {
        if (!filterText) return data;
        
        return data.filter((item: any) =>
            Object.values(item).some((value: any) =>
                value?.toString().toLowerCase().includes(filterText.toLowerCase())
            )
        );
    }, [data, filterText]);

    // Search component
    const subHeaderComponent = React.useMemo(() => {
        const handleClear = () => {
            if (filterText) {
                setResetPaginationToggle(!resetPaginationToggle);
                setFilterText('');
            }
        };

        return (
            <div className="flex items-center gap-4 mb-4">
                <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                        id="search"
                        type="text"
                        placeholder={searchPlaceholder}
                        value={filterText}
                        onChange={(e) => setFilterText(e.target.value)}
                        className="pl-10 max-w-sm"
                    />
                </div>
                {filterText && (
                    <Button variant="outline" onClick={handleClear} size="sm">
                        Clear
                    </Button>
                )}
            </div>
        );
    }, [filterText, resetPaginationToggle, searchPlaceholder]);

    return (
        <div className="space-y-4">
            {/* Header */}
            <div className="bg-red-600 text-white text-center py-2 font-bold text-sm rounded-t-lg">
                {title}
                {subtitle && <><br />{subtitle}</>}
            </div>

            {/* Search */}
            {searchable && subHeaderComponent}

            {/* DataTable */}
            <DataTable
                columns={finalColumns}
                data={filteredItems}
                pagination={pagination}
                paginationPerPage={paginationPerPage}
                paginationResetDefaultPage={resetPaginationToggle}
                striped={striped}
                highlightOnHover={highlightOnHover}
                responsive={responsive}
                customStyles={customStyles}
                dense
                fixedHeader
                fixedHeaderScrollHeight="600px"
            />
        </div>
    );
}
