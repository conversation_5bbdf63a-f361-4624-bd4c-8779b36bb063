import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface MenDifficultCase {
    id: number;
    caseloadNo: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthdate: string;
    barangay: string;
    address: string;
    municipality: string;
    province: string;
    educational: string;
    attainment: string;
    previousOffense: string;
    typeOfOffense: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    assistance2: string;
    referral: string;
    other: string;
    status: string;
}

interface MenDifficultTableProps {
    cases: MenDifficultCase[];
    onEdit?: (caseItem: MenDifficultCase) => void;
    onDelete?: (caseItem: MenDifficultCase) => void;
    onView?: (caseItem: MenDifficultCase) => void;
}

export default function MenDifficultTable({ cases, onEdit, onDelete, onView }: MenDifficultTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<MenDifficultCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: MenDifficultCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: MenDifficultCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name of Client',
            selector: (row: MenDifficultCase) => row.nameOfClient,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Age',
            selector: (row: MenDifficultCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Sex',
            selector: (row: MenDifficultCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Birthdate',
            selector: (row: MenDifficultCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Barangay',
            selector: (row: MenDifficultCase) => row.barangay,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Address',
            selector: (row: MenDifficultCase) => row.address,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Municipality',
            selector: (row: MenDifficultCase) => row.municipality,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Province',
            selector: (row: MenDifficultCase) => row.province,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Educational',
            selector: (row: MenDifficultCase) => row.educational,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Attainment',
            selector: (row: MenDifficultCase) => row.attainment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Previous Offense',
            selector: (row: MenDifficultCase) => row.previousOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Type of Offense',
            selector: (row: MenDifficultCase) => row.typeOfOffense,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Source of Referral',
            selector: (row: MenDifficultCase) => row.sourceOfReferral,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Method',
            selector: (row: MenDifficultCase) => row.method,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Initial',
            selector: (row: MenDifficultCase) => row.initial,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Assessment',
            selector: (row: MenDifficultCase) => row.assessment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Intervention',
            selector: (row: MenDifficultCase) => row.intervention,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: MenDifficultCase) => row.assistance,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Final',
            selector: (row: MenDifficultCase) => row.final,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Evaluation',
            selector: (row: MenDifficultCase) => row.evaluation,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Termination',
            selector: (row: MenDifficultCase) => row.termination,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance 2',
            selector: (row: MenDifficultCase) => row.assistance2,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Referral',
            selector: (row: MenDifficultCase) => row.referral,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Other',
            selector: (row: MenDifficultCase) => row.other,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Status',
            selector: (row: MenDifficultCase) => row.status,
            sortable: true,
            width: '100px',
        },
    ];

    return (
        <BaseDataTable
            title="Inventory of Cases of Men in Especially Difficult Circumstances"
            subtitle="Municipality of Balagtas"
            data={cases}
            columns={columns}
            searchPlaceholder="Search men in difficult circumstances cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
