import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface ReformistCase {
    id: number;
    caseloadNo: string;
    date: string;
    name: string;
    birthdate: string;
    age: number;
    address: string;
    contactNumber: string;
    typeOfViolation: string;
    dateReported: string;
    noOfMonthsForCounselling: number;
    dateFinishedCounselling: string;
    remarks: string;
}

interface ReformistTableProps {
    cases: ReformistCase[];
    onEdit?: (caseItem: ReformistCase) => void;
    onDelete?: (caseItem: ReformistCase) => void;
    onView?: (caseItem: ReformistCase) => void;
}

export default function ReformistTable({ cases, onEdit, onDelete, onView }: ReformistTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<ReformistCase>[] = [
        {
            name: 'Caseload No.',
            selector: (row: ReformistCase) => row.caseloadNo,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: ReformistCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name',
            selector: (row: ReformistCase) => row.name,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Birthdate',
            selector: (row: ReformistCase) => row.birthdate,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Age',
            selector: (row: ReformistCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Address',
            selector: (row: ReformistCase) => row.address,
            sortable: true,
            width: '200px',
        },
        {
            name: 'Contact Number',
            selector: (row: ReformistCase) => row.contactNumber,
            sortable: true,
            width: '130px',
        },
        {
            name: 'Type of Violation',
            selector: (row: ReformistCase) => row.typeOfViolation,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Date Reported',
            selector: (row: ReformistCase) => row.dateReported,
            sortable: true,
            width: '120px',
        },
        {
            name: 'No. of Months for Counselling',
            selector: (row: ReformistCase) => row.noOfMonthsForCounselling,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Date Finished Counselling',
            selector: (row: ReformistCase) => row.dateFinishedCounselling,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Remarks',
            selector: (row: ReformistCase) => row.remarks,
            sortable: true,
            width: '200px',
        },
    ];

    return (
        <BaseDataTable
            title="LIST OF REFORMIST"
            subtitle="MUNICIPALITY OF BALAGTAS"
            data={cases}
            columns={columns}
            searchPlaceholder="Search reformist cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
