import React from 'react';
import { TableColumn } from 'react-data-table-component';
import BaseDataTable from '@/components/ui/DataTable';

interface BahayKanlunganCase {
    id: number;
    caseloadNumber: string;
    date: string;
    nameOfClient: string;
    age: number;
    sex: string;
    birthday: string;
    address: string;
    barangay: string;
    municipality: string;
    province: string;
    educationalAttainment: string;
    caseCategory: string;
    sourceOfReferral: string;
    method: string;
    initial: string;
    assessment: string;
    intervention: string;
    assistance: string;
    final: string;
    evaluation: string;
    termination: string;
    referral: string;
    other: string;
    status: string;
}

interface BahayKanlunganTableProps {
    cases: BahayKanlunganCase[];
    onEdit?: (caseItem: BahayKanlunganCase) => void;
    onDelete?: (caseItem: BahayKanlunganCase) => void;
    onView?: (caseItem: BahayKanlunganCase) => void;
}

export default function BahayKanlunganTable({ cases, onEdit, onDelete, onView }: BahayKanlunganTableProps) {
    // Define columns for the DataTable
    const columns: TableColumn<BahayKanlunganCase>[] = [
        {
            name: 'Caseload Number',
            selector: (row: BahayKanlunganCase) => row.caseloadNumber,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Date',
            selector: (row: BahayKanlunganCase) => row.date,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Name of Client (Fullname)',
            selector: (row: BahayKanlunganCase) => row.nameOfClient,
            sortable: true,
            width: '180px',
        },
        {
            name: 'Age',
            selector: (row: BahayKanlunganCase) => row.age,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Sex',
            selector: (row: BahayKanlunganCase) => row.sex,
            sortable: true,
            width: '60px',
        },
        {
            name: 'Birthday',
            selector: (row: BahayKanlunganCase) => row.birthday,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Address',
            selector: (row: BahayKanlunganCase) => row.address,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Barangay',
            selector: (row: BahayKanlunganCase) => row.barangay,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Municipality',
            selector: (row: BahayKanlunganCase) => row.municipality,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Province',
            selector: (row: BahayKanlunganCase) => row.province,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Educational Attainment',
            selector: (row: BahayKanlunganCase) => row.educationalAttainment,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Case Category',
            selector: (row: BahayKanlunganCase) => row.caseCategory,
            sortable: true,
            width: '150px',
        },
        {
            name: 'Source of Referral',
            selector: (row: BahayKanlunganCase) => row.sourceOfReferral,
            sortable: true,
            width: '140px',
        },
        {
            name: 'Method',
            selector: (row: BahayKanlunganCase) => row.method,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Initial',
            selector: (row: BahayKanlunganCase) => row.initial,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Assessment',
            selector: (row: BahayKanlunganCase) => row.assessment,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Intervention',
            selector: (row: BahayKanlunganCase) => row.intervention,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Assistance',
            selector: (row: BahayKanlunganCase) => row.assistance,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Final',
            selector: (row: BahayKanlunganCase) => row.final,
            sortable: true,
            width: '100px',
        },
        {
            name: 'Evaluation',
            selector: (row: BahayKanlunganCase) => row.evaluation,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Termination',
            selector: (row: BahayKanlunganCase) => row.termination,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Referral',
            selector: (row: BahayKanlunganCase) => row.referral,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Other',
            selector: (row: BahayKanlunganCase) => row.other,
            sortable: true,
            width: '120px',
        },
        {
            name: 'Status',
            selector: (row: BahayKanlunganCase) => row.status,
            sortable: true,
            width: '100px',
        },
    ];

    return (
        <BaseDataTable
            title="MASTERLIST ADMITTED IN BAHAY KANLUNGAN"
            subtitle="BALAGTAS KANLUNGAN DROP-IN CENTER - MUNICIPALITY OF BALAGTAS"
            data={cases}
            columns={columns}
            searchPlaceholder="Search Bahay Kanlungan cases..."
            onEdit={onEdit}
            onDelete={onDelete}
            onView={onView}
            paginationPerPage={15}
        />
    );
}
