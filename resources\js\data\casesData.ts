// Generate current year for caseload numbering
const currentYear = new Date().getFullYear();

// Sample data for Reformist cases
export const reformistCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        date: '2024-01-15',
        names: '<PERSON>',
        birthdate: '1985-03-12',
        age: 39,
        address: 'Barangay Wawa, Balagtas',
        contactNumber: '09123456789',
        typeOfViolation: 'Substance Abuse',
        dateReported: '2024-01-15',
        noOfMonthsForCounselling: 6,
        dateFinishedCounselling: '',
        remarks: 'Ongoing rehabilitation program'
    },
    {
        id: 2,
        caseloadNo: `${currentYear}-002`,
        date: '2024-02-20',
        names: '<PERSON>',
        birthdate: '1988-07-08',
        age: 36,
        address: 'Barangay San Juan, Balagtas',
        contactNumber: '09987654321',
        typeOfViolation: 'Alcohol Dependency',
        dateReported: '2024-02-20',
        noOfMonthsForCounselling: 4,
        dateFinishedCounselling: '',
        remarks: 'Currently undergoing counselling sessions'
    },
    {
        id: 3,
        caseloadNo: `${currentYear}-003`,
        date: '2024-03-10',
        names: '<PERSON> <PERSON> Mendoza',
        birthdate: '1990-11-25',
        age: 34,
        address: 'Barangay Longos, Balagtas',
        contactNumber: '09456789123',
        typeOfViolation: 'Gambling Addiction',
        dateReported: '2024-03-10',
        noOfMonthsForCounselling: 8,
        dateFinishedCounselling: '2024-11-10',
        remarks: 'Successfully completed program'
    }
];

// Sample data for Bahay Kanlungan cases
export const bahayKanlunganCases = [
    {
        id: 1,
        caseloadNumber: `${currentYear}-001`,
        date: '2024-01-20',
        nameOfClient: 'Mark Anthony Reyes',
        age: 16,
        sex: 'Male',
        birthday: '2008-05-15',
        address: '123 Main Street',
        barangay: 'Santol',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educationalAttainment: 'Grade 10',
        naField: 'N/A',
        caseCategory: 'Children in Conflict with the Law',
        sourceOfReferral: 'Barangay Officials',
        method: 'Walk-in',
        initial: 'Completed',
        assessment: 'High Risk',
        intervention: 'Counseling',
        assistance: 'Psychological Support',
        final: 'Ongoing',
        evaluation: 'Monthly',
        termination: 'N/A',
        assistance2: 'Family Support',
        referral: 'DSWD',
        other: 'Educational Support',
        status: 'Active'
    },
    {
        id: 2,
        caseloadNumber: `${currentYear}-002`,
        date: '2024-02-15',
        nameOfClient: 'Joshua Miguel Torres',
        age: 17,
        sex: 'Male',
        birthday: '2007-09-22',
        address: '456 Secondary Road',
        barangay: 'Borol 1st',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educationalAttainment: 'Grade 9',
        naField: 'N/A',
        caseCategory: 'Children in Conflict with the Law',
        sourceOfReferral: 'Police',
        method: 'Referral',
        initial: 'Completed',
        assessment: 'Medium Risk',
        intervention: 'Diversion Program',
        assistance: 'Community Service',
        final: 'Ongoing',
        evaluation: 'Bi-weekly',
        termination: 'N/A',
        assistance2: 'Skills Training',
        referral: 'TESDA',
        other: 'Livelihood Program',
        status: 'Active'
    },
    {
        id: 3,
        caseloadNumber: `${currentYear}-003`,
        date: '2024-03-10',
        nameOfClient: 'Maria Isabel Cruz',
        age: 15,
        sex: 'Female',
        birthday: '2009-01-12',
        address: '789 Third Avenue',
        barangay: 'Wawa',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educationalAttainment: 'Grade 8',
        naField: 'N/A',
        caseCategory: 'Children in Need of Special Protection',
        sourceOfReferral: 'School',
        method: 'Walk-in',
        initial: 'Completed',
        assessment: 'Low Risk',
        intervention: 'Family Counseling',
        assistance: 'Educational Support',
        final: 'Completed',
        evaluation: 'Final',
        termination: '2024-06-10',
        assistance2: 'Medical Support',
        referral: 'Health Center',
        other: 'Follow-up Care',
        status: 'Closed'
    }
];

// Sample data for Children Protection cases
export const childrenProtectionCases = [
    {
        id: 1,
        caseloadNumber: `${currentYear}-001`,
        date: '2024-01-25',
        nameOfChild: 'Maria Isabel Santos',
        age: 12,
        sex: 'Female',
        birthdate: '2012-04-10',
        barangay: 'Wawa',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educational: 'Grade 6',
        attainment: 'Elementary',
        slw: 'Ana Cruz',
        caregiver: 'Grandmother',
        caseCategory: 'Physical Abuse',
        sourceOfReferral: 'School',
        method: 'Walk-in',
        initial: 'Completed',
        assessment: 'High Risk',
        intervention: 'Ongoing',
        assistance: 'Counseling',
        final: 'Pending',
        evaluation: 'In Progress',
        termination: 'N/A',
        assistance2: 'Family Support',
        referral: 'DSWD',
        other: 'Medical Check-up',
        status: 'Active'
    }
];

// Sample data for Children in Conflict cases
export const childrenConflictCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        date: '2024-02-01',
        nameOfChild: 'John Paul Martinez',
        age: 15,
        sex: 'Male',
        birthdate: '2009-08-18',
        barangay: 'San Juan',
        address: 'Balagtas',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educational: 'Grade 9',
        attainment: 'High School',
        previousOffense: 'None',
        typeOfOffense: 'Shoplifting',
        sourceOfReferral: 'Police',
        method: 'Referral',
        initial: 'Completed',
        assessment: 'Medium Risk',
        intervention: 'Diversion Program',
        assistance: 'Community Service',
        final: 'Pending',
        evaluation: 'Ongoing',
        termination: 'N/A',
        assistance2: 'Skills Training',
        referral: 'BJMP',
        other: 'Family Counseling',
        status: 'Active'
    }
];

// Sample data for Women in Difficult Circumstances
export const womenDifficultCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        date: '2024-01-30',
        nameOfClient: 'Rosa Maria Fernandez',
        age: 28,
        sex: 'Female',
        birthdate: '1996-02-14',
        barangay: 'Panginay',
        address: 'Balagtas',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educational: 'High School Graduate',
        attainment: 'Secondary',
        previousOffense: 'None',
        typeOfOffense: 'Domestic Violence Victim',
        sourceOfReferral: 'Barangay',
        method: 'Walk-in',
        initial: 'Completed',
        assessment: 'High Risk',
        intervention: 'Safe House',
        assistance: 'Legal Aid',
        final: 'Ongoing',
        evaluation: 'Monthly',
        termination: 'N/A',
        assistance2: 'Livelihood Program',
        referral: 'PAO',
        other: 'Psychological Support',
        status: 'Active'
    }
];

// Sample data for Men in Difficult Circumstances
export const menDifficultCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        date: '2024-02-05',
        nameOfClient: 'Carlos Eduardo Villanueva',
        age: 35,
        sex: 'Male',
        birthdate: '1989-06-20',
        barangay: 'Dalig',
        address: 'Balagtas',
        municipality: 'Balagtas',
        province: 'Bulacan',
        educational: 'College Graduate',
        attainment: 'Tertiary',
        previousOffense: 'None',
        typeOfOffense: 'Unemployment/Homelessness',
        sourceOfReferral: 'NGO',
        method: 'Referral',
        initial: 'Completed',
        assessment: 'Medium Risk',
        intervention: 'Job Placement',
        assistance: 'Skills Training',
        final: 'Ongoing',
        evaluation: 'Bi-weekly',
        termination: 'N/A',
        assistance2: 'Temporary Shelter',
        referral: 'TESDA',
        other: 'Mental Health Support',
        status: 'Active'
    }
];

// Sample data for OSAEC cases
export const osaecCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        date: '2024-01-25',
        names: 'Child Victim A',
        birthdate: '2010-08-15',
        age: 14,
        address: 'Barangay Santol, Balagtas',
        contactNumber: 'Confidential',
        typeOfViolation: 'Online Sexual Exploitation',
        dateReported: '2024-01-25',
        noOfMonthsForCounselling: 12,
        dateFinishedCounselling: '',
        remarks: 'Case under investigation - confidential'
    }
];

// Sample data for Child at Risk cases
export const childAtRiskCases = [
    {
        id: 1,
        caseloadNo: `${currentYear}-001`,
        dateOfReferral: '2024-02-01',
        nameOfCAR: 'Maria Santos',
        address: 'Barangay Dalig, Balagtas',
        brgy: 'Dalig',
        address2: 'Balagtas, Bulacan',
        birthdate: '2015-03-10',
        sex: 'Female',
        age: 9,
        nameOfGuardian: 'Elena Santos',
        addressOfGuardian: 'Barangay Dalig, Balagtas',
        highestEducational: 'Grade 3',
        offenseCommitted: 'None',
        remarks: 'At-risk due to family circumstances'
    },
    {
        id: 2,
        caseloadNo: `${currentYear}-002`,
        dateOfReferral: '2024-02-15',
        nameOfCAR: 'Pedro Miguel Cruz',
        address: 'Barangay Pulong Gubat, Balagtas',
        brgy: 'Pulong Gubat',
        address2: 'Balagtas, Bulacan',
        birthdate: '2014-07-22',
        sex: 'Male',
        age: 10,
        nameOfGuardian: 'Carmen Cruz',
        addressOfGuardian: 'Barangay Pulong Gubat, Balagtas',
        highestEducational: 'Grade 4',
        offenseCommitted: 'None',
        remarks: 'Referred by school for behavioral concerns'
    }
];
